# Multi-stage build to obfuscate ii_tool inside Linux environment
FROM nikolaik/python-nodejs:python3.10-nodejs24-slim AS obfuscator

# Install PyArmor in the build stage
RUN pip install pyarmor

# Copy source files and obfuscation script
WORKDIR /obfuscate
COPY src/ii_tool /obfuscate/ii_tool
COPY docker_obfuscate.py /obfuscate/obfuscate.py

# Remove .venv if it exists
RUN rm -rf /obfuscate/ii_tool/.venv

# Run obfuscation
RUN python obfuscate.py

# Main application stage
FROM nikolaik/python-nodejs:python3.10-nodejs24-slim

COPY docker/sandbox/.bashrc /root/.bashrc

RUN apt-get update && apt-get install -y \
    build-essential \
    procps \
    lsof \
    git \
    tmux \
    bc \
    net-tools \
    ripgrep \
    unzip \
    libmagic1 \
    xvfb \
    pandoc \
    weasyprint \
    libpq-dev \
    wget \
&& rm -rf /var/lib/apt/lists/*

RUN curl -fsSL https://bun.sh/install | bash
RUN curl -fsSL https://code-server.dev/install.sh | sh

RUN npm install -g playwright@1.55.0
RUN npm install -g @intelligent-internet/codex
RUN playwright install
RUN playwright install chromium
RUN playwright install-deps

# Set environment variables
ENV NODE_OPTIONS="--max-old-space-size=4096"

RUN mkdir -p /app/ii_agent

# Install the project into `/app`
WORKDIR /app/ii_agent

# Enable bytecode compilation
ENV UV_COMPILE_BYTECODE=1

# Copy from the cache instead of linking since it's a mounted volume
ENV UV_LINK_MODE=copy

# Copy dependency files first for better layer caching
COPY uv.lock pyproject.toml /app/ii_agent/

# Install the project's dependencies using the lockfile and settings
RUN --mount=type=cache,target=/root/.cache/uv \
    --mount=type=bind,source=uv.lock,target=uv.lock \
    --mount=type=bind,source=pyproject.toml,target=pyproject.toml \
    uv sync --locked --prerelease=allow --no-install-project --no-dev

# Copy obfuscated ii_tool and PyArmor runtime from build stage
COPY --from=obfuscator /obfuscate/final/ii_tool /app/ii_agent/src/ii_tool
COPY --from=obfuscator /obfuscate/final/pyarmor_runtime_000000 /app/ii_agent/src/pyarmor_runtime_000000
COPY README.md /app/ii_agent/

# Create empty ii_agent module to satisfy build requirements
RUN mkdir -p /app/ii_agent/src/ii_agent && \
    touch /app/ii_agent/src/ii_agent/__init__.py

# Copy config files
COPY docker/sandbox/codex_config.toml /root/.codex/config.toml
COPY docker/sandbox/codex_context.md /root/.codex/context.md
COPY docker/sandbox/template.css /app/template.css

# COPY Template files

COPY .templates /app/ii_agent/.templates

RUN --mount=type=cache,target=/root/.cache/uv \
    uv sync --locked --prerelease=allow --no-dev

ENV PATH="/app/ii_agent/.venv/bin:$PATH"

RUN mkdir /workspace
WORKDIR /workspace

# Create a startup script to run both services
COPY docker/sandbox/start-services.sh /app/start-services.sh
RUN chmod +x /app/start-services.sh
