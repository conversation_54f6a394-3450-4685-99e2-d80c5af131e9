from typing import Dict
from ii_tool.interfaces.sandbox import Sand<PERSON>Interface
from ii_tool.core.workspace import Workspace<PERSON>anager
from ii_tool.tools.dev.database import GetDatabaseConnection

from ii_tool.tools.shell import (
    ShellInit,
    ShellRunCommand,
    ShellView,
    ShellStopCommand,
    ShellList,
    TmuxSessionManager,
    ShellWriteToProcessTool,
)

# from ii_tool.tools.codex import CodexExecuteTool  # Now using MCP stdio versions
from ii_tool.tools.file_system import (
    ASTGrepTool,
    FileReadTool,
    FileWriteTool,
    FileEditTool,
    ApplyPatchTool,
)
from ii_tool.tools.productivity import TodoReadTool, TodoWriteTool
from ii_tool.tools.media import (
    VideoGenerateTool,
    ImageGenerateTool,
)
from ii_tool.tools.dev import FullStackInitTool, RegisterPort
from ii_tool.tools.web import (
    WebSearchTool,
    WebVisitTool,
    ImageSearchTool,
    WebVisitCompressTool,
    ReadRemoteImageTool,
    WebBatchSearchTool,
)
from ii_tool.tools.slide_system.slide_edit_tool import <PERSON><PERSON><PERSON><PERSON><PERSON>ool
from ii_tool.tools.slide_system.slide_write_tool import <PERSON><PERSON><PERSON><PERSON><PERSON>ool

# from ii_tool.tools.codex import CodexExecuteTool  # Now using MCP stdio versions
from ii_tool.tools.browser import (
    BrowserClickTool,
    BrowserWaitTool,
    BrowserViewTool,
    BrowserScrollDownTool,
    BrowserScrollUpTool,
    BrowserSwitchTabTool,
    BrowserOpenNewTabTool,
    BrowserGetSelectOptionsTool,
    BrowserSelectDropdownOptionTool,
    BrowserNavigationTool,
    BrowserRestartTool,
    BrowserEnterTextTool,
    BrowserPressKeyTool,
    BrowserDragTool,
    BrowserEnterMultipleTextsTool,
)
from ii_tool.browser.browser import Browser


def get_common_tools(
    sandbox: SandboxInterface,
):
    tools = [
        # Sandbox tools
        RegisterPort(sandbox=sandbox),
    ]

    return tools


def get_sandbox_tools(workspace_path: str, tool_server_url: str, credential: Dict):
    terminal_manager = TmuxSessionManager()
    workspace_manager = WorkspaceManager(workspace_path)
    browser = Browser()

    tools = [
        # Shell tools
        ShellInit(terminal_manager, workspace_manager),
        ShellRunCommand(terminal_manager, workspace_manager),
        ShellView(terminal_manager),
        ShellStopCommand(terminal_manager),
        ShellList(terminal_manager),
        ShellWriteToProcessTool(terminal_manager),
        # File system tools
        FileReadTool(workspace_manager),
        FileWriteTool(workspace_manager),
        FileEditTool(workspace_manager),
        ApplyPatchTool(workspace_manager),
        ASTGrepTool(workspace_manager),
        FullStackInitTool(workspace_manager),
        # Media tools
        ImageGenerateTool(tool_server_url, workspace_manager, credential),
        VideoGenerateTool(tool_server_url, workspace_manager, credential),
        # Web tools
        WebSearchTool(tool_server_url, credential),
        WebVisitTool(tool_server_url, credential),
        WebVisitCompressTool(tool_server_url, credential),
        ImageSearchTool(tool_server_url, credential),
        ReadRemoteImageTool(),
        WebBatchSearchTool(tool_server_url, credential),
        # Database tools
        GetDatabaseConnection(tool_server_url, credential),
        # Todo tools
        TodoReadTool(),
        TodoWriteTool(),
        # Slide tools
        SlideWriteTool(workspace_manager),
        SlideEditTool(workspace_manager),
        # Browser tools
        BrowserClickTool(browser),
        BrowserWaitTool(browser),
        BrowserViewTool(browser),
        BrowserScrollDownTool(browser),
        BrowserScrollUpTool(browser),
        BrowserSwitchTabTool(browser),
        BrowserOpenNewTabTool(browser),
        BrowserGetSelectOptionsTool(browser),
        BrowserSelectDropdownOptionTool(browser),
        BrowserNavigationTool(browser),
        BrowserRestartTool(browser),
        BrowserEnterTextTool(browser),
        BrowserPressKeyTool(browser),
        BrowserDragTool(browser),
        BrowserEnterMultipleTextsTool(browser),
    ]

    return tools
