from typing import List
from openai import AsyncOpenAI
from . import utils
from .base import BaseWebVisitClient, WebVisitResult
from ii_tool.integrations.llm.client import LLMClient

class WebVisitService:
    def __init__(self, web_visit_client: BaseWebVisitClient, researcher_visit_client: BaseWebVisitClient, llm_client: LLMClient):
        self.web_visit_client = web_visit_client
        self.researcher_visit_client = researcher_visit_client
        self.llm_client = llm_client

    async def visit(self, url: str, prompt: str | None = None) -> WebVisitResult:
        raw_content = await self.web_visit_client.extract(url)
        cost = raw_content.cost
        if not prompt:
            return WebVisitResult(
                content=raw_content.content,
                cost=cost,
            )

        # process the content with prompt
        formatted_prompt = utils.get_visit_webpage_prompt(raw_content, prompt)
        llm_processed = await self.llm_client.generate(formatted_prompt)
        llm_processed_content = llm_processed.content
        llm_processed_cost = llm_processed.cost
        return WebVisitResult(
            content=llm_processed_content,
            cost=cost + llm_processed_cost,
        )

    async def researcher_visit(self, urls: List[str], query: str) -> WebVisitResult:
        return await self.researcher_visit_client.batch_extract_compress(urls, query)