"""Agent type configuration and enums."""

from enum import Enum
from typing import List, Optional
from ii_agent.utils.constants import is_gpt5_family

# from ii_tool.mcp_integrations.playwright import SELECTED_TOOLS as PLAYWRIGHT_TOOLS
from ii_tool.tools.dev.database import GetDatabaseConnection
from ii_tool.tools.shell import (
    ShellView,
    ShellInit,
    ShellStopCommand,
    ShellList,
    ShellRunCommand,
)
from ii_tool.tools.file_system import (
    ASTGrepTool,
    FileEditTool,
    FileReadTool,
    FileWriteTool,
    ApplyPatchTool,
)
from ii_tool.tools.browser import (
    <PERSON>rowserClickTool,
    <PERSON>rowserWaitTool,
    BrowserViewTool,
    BrowserScrollDownTool,
    BrowserS<PERSON>rollUpTool,
    BrowserSwitchTabTool,
    BrowserOpenNewTabTool,
    BrowserGetSelectOptionsTool,
    BrowserSelectDropdownOptionTool,
    <PERSON>rowserNavigationTool,
    <PERSON>rowserRestartTool,
    <PERSON>rowserEnterTextTool,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ool,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>erEnterMultipleTextsTool,
)
from ii_tool.tools.media import <PERSON><PERSON><PERSON>ate<PERSON><PERSON>, ImageGenerateTool
from ii_tool.tools.dev import RegisterPort, FullStackInitTool
from ii_tool.tools.web import WebSearchTool, WebVisitTool, WebVisitCompressTool
from ii_tool.tools.slide_system.slide_edit_tool import SlideEditTool
from ii_tool.tools.slide_system.slide_write_tool import SlideWriteTool
from ii_tool.tools.productivity import TodoReadTool, TodoWriteTool
from ii_tool.tools.web.image_search_tool import ImageSearchTool
from ii_tool.tools.web.web_batch_search_tool import WebBatchSearchTool
# from ii_tool.tools.codex import CodexExecuteTool  # Now using MCP stdio versions


class AgentType(str, Enum):
    """Enumeration of available agent types."""

    GENERAL = "general"
    MEDIA = "media"
    BROWSER = "browser"
    SLIDE = "slide"
    RESEARCHER = "researcher"
    WEBSITE_BUILD = "website_build"
    TASK_AGENT = "task_agent"
    DESIGN_DOCUMENT = "design_document"
    CODEX = "codex"


class AgentTypeConfig:
    """Configuration for different agent types."""

    # Define toolsets for each agent type
    TOOLSETS = {
        AgentType.GENERAL: [
            # Shell tools
            ShellInit.name,
            ShellRunCommand.name,
            ShellView.name,
            ShellStopCommand.name,
            ShellList.name,
            # File system tools
            ASTGrepTool.name,
            FileReadTool.name,
            FileWriteTool.name,
            FileEditTool.name,
            FullStackInitTool.name,
            # Web tools
            WebSearchTool.name,
            WebVisitTool.name,
            ImageSearchTool.name,
            TodoReadTool.name,
            TodoWriteTool.name,
            RegisterPort.name,
            FullStackInitTool.name,
        ],
        AgentType.TASK_AGENT: [
            # Shell tools
            ShellInit.name,
            ShellRunCommand.name,
            ShellView.name,
            ShellStopCommand.name,
            ShellList.name,
            # File system tools
            FileReadTool.name,
            FileWriteTool.name,
            FileEditTool.name,
            FullStackInitTool.name,
            # Web tools
            WebSearchTool.name,
            WebVisitTool.name,
            ImageSearchTool.name,
            TodoReadTool.name,
            TodoWriteTool.name,
        ],
        AgentType.BROWSER: [
            # Browser tools
            BrowserClickTool.name,
            BrowserWaitTool.name,
            BrowserViewTool.name,
            BrowserScrollDownTool.name,
            BrowserScrollUpTool.name,
            BrowserSwitchTabTool.name,
            BrowserOpenNewTabTool.name,
            BrowserGetSelectOptionsTool.name,
            BrowserSelectDropdownOptionTool.name,
            BrowserNavigationTool.name,
            BrowserRestartTool.name,
            BrowserEnterTextTool.name,
            BrowserPressKeyTool.name,
            BrowserDragTool.name,
            BrowserEnterMultipleTextsTool.name,
        ],
        AgentType.MEDIA: [
            # File system tools
            FileReadTool.name,
            # Media tools
            VideoGenerateTool.name,
            ImageGenerateTool.name,
            # Web tools
            WebSearchTool.name,
            WebVisitTool.name,
            # Todo tools
            TodoReadTool.name,
            TodoWriteTool.name,
        ],
        AgentType.SLIDE: [
            # TODO: Add slide-specific tools when available
            FileEditTool.name,
            FileReadTool.name,
            FileWriteTool.name,
            ImageGenerateTool.name,
            WebSearchTool.name,
            WebVisitTool.name,
            ImageSearchTool.name,
            TodoReadTool.name,
            TodoWriteTool.name,
            SlideWriteTool.name,
            SlideEditTool.name,
        ],
        AgentType.WEBSITE_BUILD: [
            # Shell tools
            ShellInit.name,
            ShellRunCommand.name,
            ShellView.name,
            ShellStopCommand.name,
            ShellList.name,
            # File system tools
            FileReadTool.name,
            FileWriteTool.name,
            FileEditTool.name,
            FullStackInitTool.name,
            # Web tools
            WebSearchTool.name,
            WebVisitTool.name,
            # Todo tools
            TodoReadTool.name,
            TodoWriteTool.name,
            # common tools
            RegisterPort.name,
            GetDatabaseConnection.name,
        ],
        AgentType.RESEARCHER: [
            WebBatchSearchTool.name,
            WebVisitCompressTool.name,
        ],
        AgentType.DESIGN_DOCUMENT: [
            # File system tools for creating design docs
            ShellInit.name,
            ShellRunCommand.name,
            ShellView.name,
            ShellStopCommand.name,
            ShellList.name,
            # File system tools
            FileReadTool.name,
            FileWriteTool.name,
            FileEditTool.name,
            WebSearchTool.name,
            WebVisitTool.name,
            ImageSearchTool.name,
            TodoReadTool.name,
            TodoWriteTool.name,
        ],
        AgentType.CODEX: [
            ShellInit.name,
            ShellRunCommand.name,
            ShellView.name,
            ShellStopCommand.name,
            ShellList.name,
            # File system tools
            ASTGrepTool.name,
            FileReadTool.name,
            FileWriteTool.name,
            FileEditTool.name,
            # Web tools
            WebSearchTool.name,
            WebVisitTool.name,
            ImageSearchTool.name,
            TodoReadTool.name,
            TodoWriteTool.name,
            RegisterPort.name,
            FullStackInitTool.name,
            # Codex tools are now provided via MCP stdio transport
            #"mcp_codex_execute",  # MCP Codex execute tool
            #"mcp_codex_review",  # MCP Codex review tool
        ],
    }

    GENERAL_FORBIDDEN_TOOLS = [
        SlideEditTool.name,
        SlideWriteTool.name,
    ]

    @classmethod
    def get_allowed_toolset(
        cls, agent_type: AgentType, model_name: Optional[str] = None
    ) -> List[str]:
        """Get the allowed toolset for a specific agent type.

        Args:
            agent_type: The type of agent
            model_name: Optional model name to customize toolset (e.g., "gpt-5")

        Returns:
            List of allowed tool names
        """
        base_toolset = cls.TOOLSETS.get(
            agent_type, cls.TOOLSETS[AgentType.GENERAL]
        ).copy()

        # If model is GPT-5 family, replace FileWriteTool and FileEditTool with ApplyPatchTool
        if model_name and is_gpt5_family(model_name):
            # Remove FileWriteTool and FileEditTool if present
            base_toolset = [
                tool
                for tool in base_toolset
                if tool not in [FileWriteTool.name, FileEditTool.name]
            ]

            # Add ApplyPatchTool if not already present
            if ApplyPatchTool.name not in base_toolset:
                # Find position where file tools are (after FileReadTool)
                try:
                    read_index = base_toolset.index(FileReadTool.name)
                    base_toolset.insert(read_index + 1, ApplyPatchTool.name)
                except ValueError:
                    # If FileReadTool not found, just append
                    base_toolset.append(ApplyPatchTool.name)

        return base_toolset

    @classmethod
    def is_valid_agent_type(cls, agent_type: str) -> bool:
        """Check if an agent type is valid."""
        try:
            AgentType(agent_type)
            return True
        except ValueError:
            return False

    @classmethod
    def get_all_agent_types(cls) -> List[str]:
        """Get all available agent types."""
        return [agent_type.value for agent_type in AgentType]
