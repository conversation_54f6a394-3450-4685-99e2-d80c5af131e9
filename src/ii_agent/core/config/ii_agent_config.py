import os
from typing import Optional, Dict, Any
from pydantic import Field, computed_field, field_validator, model_validator
from pydantic_settings import BaseSettings, SettingsConfigDict
from ii_agent.core.config.llm_config import LLMConfig
from ii_agent.utils.constants import TOKEN_BUDGET
from pathlib import Path

# Constants
MAX_OUTPUT_TOKENS_PER_TURN = 32000
MAX_TURNS = 200

II_AGENT_DIR = Path(__file__).parent.parent.parent


class ResearcherAgentConfig(BaseSettings):
    researcher: LLMConfig
    report_builder: LLMConfig
    final_report_builder: LLMConfig


class IIAgentConfig(BaseSettings):
    """
    Configuration for the IIAgent.

    Attributes:
        file_store: The type of file store to use.
        file_store_path: The path to the file store.
    """

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        extra="ignore",
        env_nested_delimiter="__",
        nested_model_default_partial_update=True,
    )
    file_store: str = Field(default="local")
    file_store_path: str = Field(default="~/.ii_agent")
    use_container_workspace: bool = Field(default=False)
    minimize_stdout_logs: bool = False
    docker_container_id: Optional[str] = None
    max_output_tokens_per_turn: int = MAX_OUTPUT_TOKENS_PER_TURN
    max_turns: int = MAX_TURNS
    token_budget: int = TOKEN_BUDGET
    database_url: Optional[str] = None
    mcp_config: Optional[Dict[str, Any]] = None
    mcp_port: int = Field(default=6060)
    vscode_port: int = Field(default=9000)
    mcp_timeout: int = Field(default=1800)

    # Storage configuration
    # File upload storage
    storage_provider: str = Field(default="gcs")
    file_upload_project_id: str | None = None
    file_upload_bucket_name: str | None = None
    file_upload_size_limit: int = Field(default=100 * 1024 * 1024)  # 100MB default
    # Avatar storage
    avatar_project_id: str | None = None
    avatar_bucket_name: str | None = None

    # Slide assets storage (for permanent URLs)
    slide_assets_project_id: str | None = None
    slide_assets_bucket_name: str | None = None

    # Custom domain for permanent URLs
    custom_domain: str | None = Field(
        default=None,
        description="Custom domain for permanent file URLs (e.g., 'files.yourdomain.com')",
    )

    # TODO: LLM configuration
    llm_configs: dict[str, LLMConfig]
    researcher_agent_config: ResearcherAgentConfig
    # Google OAuth configuration
    google_client_id: str = Field(default="")
    google_client_secret: str = Field(default="")
    google_redirect_uri: str = Field(
        default="http://localhost:8000/auth/oauth/google/callback"
    )

    # Stripe configuration
    stripe_secret_key: str | None = None
    stripe_price_plus_monthly: str | None = None
    stripe_price_plus_annually: str | None = None
    stripe_price_pro_monthly: str | None = None
    stripe_price_pro_annually: str | None = None
    stripe_return_url: str | None = None
    stripe_success_url: str | None = None
    stripe_cancel_url: str | None = None
    stripe_webhook_secret: str | None = None
    stripe_portal_return_url: str | None = None

    # Credits configuration
    default_user_credits: float = Field(
        default=300.0,
        description="Default credits for new users",
        ge=0.0,  # Must be non-negative
    )

    default_subscription_plan: str = Field(
        default="free",
        description="Default subscription plan for new users",
    )

    default_plans_credits: Dict[str, float] = {
        "free": 300.0,
        "plus": 2000.0,
        "pro": 10000.0,
    }

    beta_program_enabled: bool = Field(
        default=True,
        description="Toggle for granting beta program bonus credits on login",
    )
    beta_program_bonus_credits: float = Field(
        default=2000.0,
        description="Bonus credits granted to beta program users",
        ge=0.0,
    )

    # Per session config
    # TODO: move to a separate class
    session_id: Optional[str] = None
    auto_approve_tools: bool = False  # Global tool approval setting. If True, all tools will be automatically approved.
    allow_tools: set[str] = set()  # Tools that are confirmed by the user

    # Sandbox configuration
    workspace_path: str = Field(default="/workspace")  # workspace path in sandbox
    workspace_upload_subpath: str = Field(
        default="uploads"
    )  # upload subpath in workspace

    # Tool server configuration
    tool_server_url: str
    # Sandbox server configuration
    sandbox_server_url: str = Field(default="http://localhost:8100")
    sandbox_template_id: str | None = Field(default=None)
    environment: str = Field(default="dev")

    @model_validator(mode="after")
    def set_database_url(self) -> "IIAgentConfig":
        if self.database_url is None:
            # Default to PostgreSQL connection
            # You can set DATABASE_URL environment variable or it will use this default
            self.database_url = os.getenv(
                "DATABASE_URL",
                "postgresql+asyncpg://postgres:postgres@localhost:5432/ii_agent",
            )

        return self

    @computed_field
    @property
    def sync_database_url(self) -> str:
        """Return the synchronous database URL."""
        # Convert async PostgreSQL URL to sync
        if "+asyncpg" in self.database_url:
            return self.database_url.replace("+asyncpg", "")
        # Keep backward compatibility for SQLite
        elif "+aiosqlite" in self.database_url:
            return self.database_url.replace("+aiosqlite", "")
        return self.database_url

    @computed_field
    @property
    def logs_path(self) -> str:
        return os.path.join(self.file_store_path, "logs")

    @computed_field
    @property
    def workspace_upload_path(self) -> str:
        return os.path.join(self.workspace_path, self.workspace_upload_subpath)

    @field_validator("file_store_path")
    def expand_path(cls, v):
        if v.startswith("~"):
            return os.path.expanduser(v)
        return v

    def set_auto_approve_tools(self, value: bool) -> None:
        """Set the auto_approve_tools field value.

        Args:
            value: Whether to automatically approve tool executions in CLI mode
        """
        self.auto_approve_tools = value


config = IIAgentConfig()
print(config)
