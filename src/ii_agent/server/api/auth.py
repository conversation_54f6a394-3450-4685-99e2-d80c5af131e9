"""Authentication API endpoints."""

from typing import Any
import uuid
from datetime import datetime, timezone
from fastapi import APIRouter, HTTPException, Request, status
from fastapi_sso.sso.google import GoogleSSO
from sqlalchemy import select, func

from ii_agent.db.models import User, <PERSON>Key, WaitlistEntry
from ii_agent.server.api.deps import SessionDep
from ii_agent.server.auth.middleware import CurrentUser
from ii_agent.server.models.auth import (
    TokenResponse,
)
from ii_agent.server.auth.jwt_handler import jwt_handler
from ii_agent.core.config.ii_agent_config import config
from ii_agent.server.models.users import UserPublic
from ii_agent.server.auth.api_key_utils import generate_prefixed_api_key


router = APIRouter(prefix="/auth", tags=["Authentication"])


@router.get("/oauth/google/login")
async def google_login():
    """Redirect to Google SSO login."""

    google_sso = GoogleSSO(
        config.google_client_id or "",
        config.google_client_secret or "",
        redirect_uri=config.google_redirect_uri,
    )
    async with google_sso:
        return await google_sso.get_login_redirect(
            params={"prompt": "consent", "access_type": "offline"}
        )


@router.get("/oauth/google/callback")
async def google_callback(
    request: Request,
    db: SessionDep,
):
    """Handle Google SSO callback and login."""
    url = request.query_params.get("redirect_uri")
    google_sso = GoogleSSO(
        config.google_client_id or "",
        config.google_client_secret or "",
        redirect_uri=(url or config.google_redirect_uri),
    )
    async with google_sso:
        user_info = await google_sso.verify_and_process(request)
    if not user_info:
        raise ValueError("Failed to get user info from Google SSO")

    email = (user_info.email or "").strip().lower()
    if not email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email not provided by Google account",
        )

    waitlist_result = await db.execute(
        select(WaitlistEntry).where(func.lower(WaitlistEntry.email) == email)
    )
    waitlist_entry = waitlist_result.scalar_one_or_none()

    if not email.endswith("@ii.inc") and waitlist_entry is None:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Your account is not on the waitlist.",
        )

    result = await db.execute(select(User).where(func.lower(User.email) == email))
    user_stored = result.scalar_one_or_none()
    if not user_stored:
        # Register new user if not exists
        new_user = User(
            id=str(uuid.uuid4()),
            email=email,
            first_name=user_info.first_name or "",
            last_name=user_info.last_name or "",
            avatar=user_info.picture or None,
            role="user",
            is_active=True,
            email_verified=True,
            credits=config.default_user_credits,  # Initial signup credits from config
            bonus_credits=(
                config.beta_program_bonus_credits
                if config.beta_program_enabled
                else 0.0
            ),
            created_at=datetime.now(timezone.utc),
            subscription_plan=config.default_subscription_plan,
        )
        db.add(new_user)
        await db.commit()
        await db.refresh(new_user)
        
        # Generate API key for the new user
        api_key = APIKey(
            id=str(uuid.uuid4()),
            user_id=new_user.id,
            api_key=generate_prefixed_api_key(),
            is_active=True,
            created_at=datetime.now(timezone.utc),
        )
        db.add(api_key)
        await db.commit()
        user_stored = new_user

    access_token = jwt_handler.create_access_token(
        user_id=str(user_stored.id),
        email=str(user_stored.email),
        role=str(user_stored.role),
    )

    refresh_token = jwt_handler.create_refresh_token(user_id=str(user_stored.id))

    return TokenResponse(
        access_token=access_token,
        refresh_token=refresh_token,
        expires_in=jwt_handler.access_token_expire_minutes * 60,
    )


@router.get("/me", response_model=UserPublic)
async def reader_user_me(current_user: CurrentUser) -> Any:
    return current_user
