import logging
from contextlib import asynccontextmanager
from fastapi import <PERSON>AP<PERSON>, APIRouter
from fastapi.middleware.cors import CORSMiddleware
import socketio

from .api import (
    sessions_router,
    llm_settings_router,
    auth_router,
    files_router,
    mcp_settings_router,
    enhance_prompt_router,
    wishlist_router,
    billing_router,
)
from .slides.views import router as slides_router
from .credits import credits_router
from ii_agent.server import shared
from ii_agent.server.auth.jwt_handler import jwt_handler
from ii_agent.server.websocket.manager import ConnectionManager
from ii_agent.core.config.ii_agent_config import config

logger = logging.getLogger(__name__)


health_router = APIRouter()


@health_router.get("/health")
async def health_check():
    return {"status": "ok"}


def setup_socketio_handlers(sio: socketio.AsyncServer, app: FastAPI):
    """Setup Socket.IO event handlers."""

    @sio.event
    async def connect(sid, environ, auth):
        """Handle Socket.IO client connection."""
        logger.info(f"Socket.IO client connecting: {sid}")

        # Extract authentication info
        if not auth or "token" not in auth:
            logger.warning(
                f"Socket.IO connection rejected: No authentication token provided for {sid}"
            )
            return False

        auth_token = auth["token"]
        session_uuid_str = auth.get("session_uuid")

        # Try to authenticate
        try:
            # Verify the access token
            payload = jwt_handler.verify_access_token(auth_token)
            if payload:
                user_id = payload.get("user_id")
                logger.info(f"Socket.IO authenticated for user: {user_id}")

                # Store connection info for later use
                # Don't call connection_manager.connect here as the socket isn't ready yet
                # We'll handle this after the connection is fully established
                await sio.save_session(
                    sid,
                    {
                        "user_id": user_id,
                        "session_uuid": session_uuid_str,
                        "authenticated": True,
                    },
                )

                return True
            else:
                logger.warning(
                    f"Socket.IO connection rejected: Invalid or expired token for {sid}"
                )
                return False
        except Exception as e:
            logger.error(
                f"Socket.IO connection rejected: Error verifying token for {sid}: {e}"
            )
            return False

    @sio.event
    async def leave_session(sid, data):
        """Leave the session."""
        logger.info(f"Socket.IO client leaving session: {sid}")
        await app.state.connection_manager.disconnect(sid)

    @sio.event
    async def join_session(sid, data):
        """Join the session after connection is fully established."""
        try:
            # Get the stored session data
            session_data = await sio.get_session(sid)
            if not session_data or not session_data.get("authenticated"):
                logger.error(f"No valid session data found for {sid}")
                await sio.disconnect(sid)
                return

            user_id = session_data.get("user_id")
            session_uuid_str = data.get("session_uuid")

            logger.info(f"Joining session for {session_uuid_str}, user: {user_id}")

            # Now we can safely create the session
            session = await app.state.connection_manager.connect(
                sid, session_uuid_str=session_uuid_str, user_id=user_id
            )

            # Send handshake event
            await session.handshake(is_new_session=not session_uuid_str)

        except Exception as e:
            logger.error(f"Error initializing session for {sid}: {e}", exc_info=True)
            # Send error event before disconnecting
            try:
                await sio.emit(
                    "chat_event",
                    {
                        "type": "error",
                        "content": {
                            "message": f"Session initialization failed: {str(e)}"
                        },
                    },
                    room=sid,
                )
            except Exception:
                pass  # If we can't send the error, just log it
            await sio.disconnect(sid)

    @sio.event
    async def disconnect(sid):
        """Handle Socket.IO client disconnection."""
        await app.state.connection_manager.disconnect(sid)

    @sio.event
    async def chat_message(sid, data):
        """Handle incoming chat messages."""
        logger.info(f"Received chat message from {sid}: {data}")

        try:
            # First try to get session by socket ID
            await app.state.connection_manager.handle_message(sid, data)
        except Exception as e:
            logger.error(f"Error handling chat message from {sid}: {e}", exc_info=True)
            try:
                await sio.emit(
                    "chat_event",
                    {
                        "type": "error",
                        "content": {"message": f"Error processing message: {str(e)}"},
                    },
                    room=sid,
                )
            except Exception:
                pass  # If we can't send the error, just log it


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan events."""
    yield
    # Shutdown (if needed)


def create_app():
    """Create and configure the FastAPI application with Socket.IO integration.

    Returns:
        socketio.ASGIApp: Configured Socket.IO application instance
    """

    docs_enabled = config.environment != "production"

    # Create FastAPI app
    app = FastAPI(
        title="Agent Socket.IO API",
        lifespan=lifespan,
        docs_url="/docs" if docs_enabled else None,
        redoc_url="/redoc" if docs_enabled else None,
        openapi_url="/openapi.json" if docs_enabled else None,
    )

    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Allows all origins
        allow_credentials=True,
        allow_methods=["*"],  # Allows all methods
        allow_headers=["*"],  # Allows all headers
    )

    # Store global args in app state for access in endpoints
    app.state.workspace = shared.config.workspace_path

    # Create connection manager instance
    app.state.connection_manager = ConnectionManager(
        message_service=shared.message_service,
        session_service=shared.session_service,
        sandbox_service=shared.sandbox_service,
        config=shared.config,
    )

    # Include API routers (organized by domain)
    app.include_router(auth_router)  # /auth/*
    app.include_router(sessions_router)  # /sessions/*
    app.include_router(credits_router)  # /credits/*
    app.include_router(llm_settings_router)  # /user-settings/llm/*
    app.include_router(mcp_settings_router)  # /user-settings/mcp/*
    app.include_router(files_router)  # /files/*
    app.include_router(slides_router)  # /slides/*
    app.include_router(wishlist_router)  # /wishlist/*
    app.include_router(enhance_prompt_router)  # /enhance-prompt/*
    app.include_router(billing_router)  # /billing/*
    app.include_router(health_router)

    # Create Socket.IO server with increased timeout settings
    sio = socketio.AsyncServer(
        async_mode="asgi",
        cors_allowed_origins="*",
        ping_timeout=300,  # 120 seconds before considering connection dead (default is 20s)
        ping_interval=30,  # Send ping every 30 seconds (default is 25s)
        max_http_buffer_size=10 * 1024 * 1024,  # 10MB max message size
    )

    app.state.sio = sio

    app.state.connection_manager.set_sio(sio)

    # Setup Socket.IO event handlers
    setup_socketio_handlers(sio, app)

    # Create Socket.IO ASGI app that wraps FastAPI
    socket_app = socketio.ASGIApp(sio, app)

    return socket_app
