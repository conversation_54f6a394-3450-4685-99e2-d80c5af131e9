"""Service layer for metrics database operations."""

import logging
from typing import Optional, Dict, Any
from datetime import datetime
from ii_agent.db.models import SessionMetrics, Session
from sqlalchemy import select

from ii_agent.db.manager import DBSession

logger = logging.getLogger(__name__)


async def accumulate_session_metrics(
    *, db_session: DBSession, session_id: str, credits: float
) -> bool:
    """Accumulate credits for a session by adding to existing total.

    NOTE: Credits should be passed as NEGATIVE values to represent consumption.
    The function uses += operator, so negative values will decrease the session total.
    For example: passing credits=-5.0 will reduce the session's credit balance by 5.

    Args:
        db_session: Database session
        session_id: The session ID to accumulate credits for
        credits: Number of credits to add to the session (pass negative values for consumption)
    """
    try:
        # Import here to avoid circular dependencies

        # Check if metrics record exists
        existing = (
            await db_session.execute(
                select(SessionMetrics).where(SessionMetrics.session_id == session_id)
            )
        ).scalar_one_or_none()

        if existing:
            # Accumulate to existing record
            existing.credits += credits
            existing.updated_at = datetime.utcnow()
        else:
            # Create new record with initial credits
            metrics_record = SessionMetrics(
                session_id=session_id,
                credits=credits,
            )
            db_session.add(metrics_record)

        await db_session.commit()
        logger.debug(f"Accumulated credits in database for session {session_id}")
        return True
    except ImportError:
        # SessionMetrics model doesn't exist yet
        logger.debug("SessionMetrics model not yet available in database")
        return False
    except Exception as e:
        logger.error(f"Error accumulating metrics in database: {e}", exc_info=True)
        raise


async def get_session_metrics(
    *, db_session: DBSession, session_id: str
) -> Optional[Dict[str, Any]]:
    """Get credits metrics for a specific session.

    Args:
        db_session: Database session
        session_id: The session ID to get credits for

    Returns:
        Dictionary containing session credits or None if not found
    """
    try:
        # Import here to avoid circular dependencies
        from ii_agent.db.models import SessionMetrics
        from sqlalchemy import select

        metrics = (
            await db_session.execute(
                select(SessionMetrics).where(SessionMetrics.session_id == session_id)
            )
        ).scalar_one_or_none()

        if metrics:
            return {
                "session_id": metrics.session_id,
                "credits": metrics.credits,
                "created_at": metrics.created_at,
                "updated_at": metrics.updated_at,
            }

        return None

    except ImportError:
        # SessionMetrics model doesn't exist yet
        logger.debug("SessionMetrics model not yet available in database")
        return None
    except Exception as e:
        logger.error(f"Error getting metrics from database: {e}", exc_info=True)
        raise


async def is_user_provided_model(*, db_session: DBSession, session_id: str) -> bool:
    """Check if session uses user-provided model configuration.

    Args:
        db_session: Database session
        session_id: The session ID to check

    Returns:
        True if session uses user-provided model (has llm_setting_id), False otherwise
    """
    try:
        result = await db_session.execute(
            select(Session.llm_setting_id).where(Session.id == session_id)
        )
        llm_setting_id = result.scalar_one_or_none()

        # If session has llm_setting_id, it's using user's model configuration
        is_user_model = llm_setting_id is not None

        logger.debug(
            f"Session {session_id} model source: "
            f"{'user-provided' if is_user_model else 'system-provided'}"
        )

        return is_user_model

    except Exception as e:
        logger.error(
            f"Error checking model source for session {session_id}: {e}", exc_info=True
        )
        # Default to system model on error (safer to charge than not charge)
        return False
