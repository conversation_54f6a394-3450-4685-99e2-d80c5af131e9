{"name": "ii-agent", "version": "1.0.0", "description": "II-Agent is an open-source intelligent assistant designed to streamline and enhance workflows across multiple domains. It represents a significant advancement in how we interact with technology—shifting from passive tools to intelligent systems capable of independently executing complex tasks.", "keywords": ["agent", "ai"], "license": "MIT", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri", "prepare": "husky", "lint": "eslint . --report-unused-disable-directives --max-warnings 0", "format": "prettier --write ."}, "lint-staged": {"**/*": "prettier --write --ignore-unknown"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@monaco-editor/react": "^4.7.0", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tooltip": "^1.2.7", "@react-oauth/google": "^0.12.2", "@reduxjs/toolkit": "^2.8.2", "@stripe/stripe-js": "^7.9.0", "@tailwindcss/vite": "^4.1.5", "@tanstack/react-table": "^8.21.3", "@tauri-apps/api": "^2.5.0", "@tauri-apps/plugin-process": "^2.2.1", "@tauri-apps/plugin-shell": "^2.2.1", "@types/lodash": "^4.17.20", "@types/react-window": "^1.8.8", "@xterm/addon-fit": "^0.10.0", "@xterm/xterm": "^5.5.0", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "framer-motion": "^12.23.6", "katex": "^0.16.22", "lodash": "^4.17.21", "lottie-react": "^2.4.1", "lucide-react": "^0.511.0", "monaco-editor": "^0.52.2", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.60.0", "react-inlinesvg": "^4.2.0", "react-markdown": "^10.1.0", "react-redux": "^9.2.0", "react-resizable-panels": "^3.0.5", "react-router": "^7.5.3", "react-window": "^2.0.2", "redux": "^5.0.1", "redux-persist": "^6.0.0", "rehype-highlight": "^7.0.2", "rehype-katex": "^7.0.1", "rehype-mathjax": "^7.1.0", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "socket.io-client": "^4.8.1", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "zod": "^4.0.5"}, "devDependencies": {"@eslint/js": "^9.25.1", "@tauri-apps/cli": "^2.5.0", "@types/node": "^22.15.3", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.3", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-react": "^7.37.5", "globals": "^16.0.0", "husky": "^9.1.7", "lint-staged": "^15.5.1", "prettier": "3.5.3", "tailwindcss": "^4.1.5", "terser": "^5.43.1", "tw-animate-css": "^1.3.5", "typescript": "^5.8.3", "typescript-eslint": "^8.31.1", "vite": "^6.3.4", "vite-plugin-svgr": "^4.3.0"}}