import { Link, Outlet } from 'react-router'
import { useAuth } from '@/contexts/auth-context'
import { useEffect } from 'react'
import { useNavigate } from 'react-router'

export function AuthLayout() {
    const { isAuthenticated } = useAuth()
    const navigate = useNavigate()

    useEffect(() => {
        if (isAuthenticated) {
            navigate('/')
        }
    }, [isAuthenticated, navigate])

    return (
        <div className="flex flex-col h-screen justify-between px-6 pt-8 pb-12 overflow-auto">
            <div className="flex items-center gap-x-3">
                <img
                    src="/images/logo-only.svg"
                    className="size-10 hidden dark:inline"
                    alt="Logo"
                />
                <img
                    src="/images/logo-charcoal.svg"
                    className="size-10 inline dark:hidden"
                    alt="Logo"
                />
                <span className="text-black dark:text-white text-2xl font-semibold">
                    II-Agent
                </span>
            </div>
            <div className="flex-1">
                <Outlet />
            </div>
            <div className="flex justify-center gap-x-10">
                <Link
                    to="https://www.ii.inc/web/terms-and-conditions"
                    target="_blank"
                    className="dark:text-white text-sm font-semibold"
                >
                    Terms of Use
                </Link>
                <Link
                    to="https://www.ii.inc/web/privacy-policy"
                    target="_blank"
                    className="dark:text-white text-sm font-semibold"
                >
                    Privacy Policy
                </Link>
            </div>
        </div>
    )
}
